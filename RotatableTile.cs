using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections;

[RequireComponent(typeof(Button))]
public class RotatableTile : MonoBehaviour, IPointerClickHandler
{
    [Header("Tile Settings")]
    public float rotationSpeed = 360f; // Degrees per second for rotation animation
    public float correctAngle = 0f; // The correct angle for this tile (usually 0)
    
    [Header("Visual Feedback")]
    public Color correctColor = Color.green;
    public Color incorrectColor = Color.white;
    public bool showColorFeedback = false; // Optional visual feedback
    
    private Button tileButton;
    private Image tileImage;
    private RectTransform rectTransform;
    private TileRotationGameManager gameManager;
    
    private float currentRotation = 0f;
    private bool isInteractable = false;
    private bool isRotating = false;
    private Color originalColor;
    
    void Awake()
    {
        // Get required components
        tileButton = GetComponent<Button>();
        tileImage = GetComponent<Image>();
        rectTransform = GetComponent<RectTransform>();
        
        // Store original color
        if (tileImage != null)
        {
            originalColor = tileImage.color;
        }
    }
    
    void Start()
    {
        // Initialize tile
        ResetTile();
        
        // Setup button click listener
        if (tileButton != null)
        {
            tileButton.onClick.AddListener(OnTileClicked);
        }
    }
    
    public void SetGameManager(TileRotationGameManager manager)
    {
        gameManager = manager;
    }
    
    public void SetInteractable(bool interactable)
    {
        isInteractable = interactable;
        
        if (tileButton != null)
        {
            tileButton.interactable = interactable;
        }
        
        // Update visual feedback if enabled
        if (showColorFeedback)
        {
            UpdateVisualFeedback();
        }
    }
    
    public void OnPointerClick(PointerEventData eventData)
    {
        OnTileClicked();
    }
    
    void OnTileClicked()
    {
        if (!isInteractable || isRotating) return;
        
        // Rotate by 90 degrees
        RotateBy90Degrees();
    }
    
    public void RotateBy90Degrees()
    {
        if (isRotating) return;
        
        float targetRotation = currentRotation + 90f;
        
        // Normalize rotation to 0-360 range
        targetRotation = NormalizeAngle(targetRotation);
        
        StartCoroutine(RotateToAngle(targetRotation, 90f / rotationSpeed));
    }
    
    public IEnumerator RotateToAngle(float targetAngle, float duration)
    {
        if (isRotating) yield break;
        
        isRotating = true;
        
        float startRotation = currentRotation;
        targetAngle = NormalizeAngle(targetAngle);
        
        // Handle rotation direction for shortest path
        float rotationDifference = targetAngle - startRotation;
        if (rotationDifference > 180f)
        {
            rotationDifference -= 360f;
        }
        else if (rotationDifference < -180f)
        {
            rotationDifference += 360f;
        }
        
        float actualTargetRotation = startRotation + rotationDifference;
        
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            float t = elapsed / duration;
            // Use smooth easing
            t = Mathf.SmoothStep(0f, 1f, t);
            
            float currentAngle = Mathf.Lerp(startRotation, actualTargetRotation, t);
            
            // Apply rotation to the RectTransform
            rectTransform.localEulerAngles = new Vector3(0, 0, currentAngle);
            
            elapsed += Time.deltaTime;
            yield return null;
        }
        
        // Ensure final rotation is exact
        currentRotation = NormalizeAngle(targetAngle);
        rectTransform.localEulerAngles = new Vector3(0, 0, currentRotation);
        
        isRotating = false;
        
        // Update visual feedback
        if (showColorFeedback)
        {
            UpdateVisualFeedback();
        }
        
        // Notify game manager
        if (gameManager != null)
        {
            gameManager.OnTileRotated(this);
        }
    }
    
    public bool IsAtCorrectAngle()
    {
        // Check if current rotation matches the correct angle (with small tolerance)
        float angleDifference = Mathf.Abs(Mathf.DeltaAngle(currentRotation, correctAngle));
        return angleDifference < 1f; // 1 degree tolerance
    }
    
    public float GetCurrentRotation()
    {
        return currentRotation;
    }
    
    public void ResetTile()
    {
        currentRotation = correctAngle;
        rectTransform.localEulerAngles = new Vector3(0, 0, currentRotation);
        isRotating = false;
        
        // Reset visual feedback
        if (tileImage != null)
        {
            tileImage.color = originalColor;
        }
    }
    
    void UpdateVisualFeedback()
    {
        if (tileImage == null) return;
        
        if (IsAtCorrectAngle())
        {
            tileImage.color = correctColor;
        }
        else
        {
            tileImage.color = incorrectColor;
        }
    }
    
    float NormalizeAngle(float angle)
    {
        while (angle < 0f)
        {
            angle += 360f;
        }
        while (angle >= 360f)
        {
            angle -= 360f;
        }
        return angle;
    }
    
    // Public method to set a specific rotation (useful for testing or setup)
    public void SetRotation(float angle)
    {
        currentRotation = NormalizeAngle(angle);
        rectTransform.localEulerAngles = new Vector3(0, 0, currentRotation);
        
        if (showColorFeedback)
        {
            UpdateVisualFeedback();
        }
    }
    
    // Public method to get rotation in 90-degree steps (0, 1, 2, 3)
    public int GetRotationStep()
    {
        return Mathf.RoundToInt(currentRotation / 90f) % 4;
    }
    
    void OnValidate()
    {
        // Ensure correct angle is normalized
        correctAngle = NormalizeAngle(correctAngle);
    }
}
