using UnityEngine;
using System.Collections;

/// <summary>
/// Reusable component for mini-game completion effects
/// Can be used by any mini-game that needs standardized completion animations
/// </summary>
public class MiniGameCompletionEffects : MonoBehaviour
{
    [Header("Completion Objects")]
    [Tooltip("Object that gets activated immediately on completion")]
    public GameObject objectToActivate;
    
    [Toolt<PERSON>("Object that gets scaled and shakes")]
    public Transform objectToScale;
    
    [<PERSON>lt<PERSON>("Objects activated after shake animation")]
    public GameObject[] objectsToActivateAfterShake = new GameObject[2];
    
    [Tooltip("Objects deactivated after shake animation")]
    public GameObject[] objectsToDeactivateAfterShake = new GameObject[2];
    
    [<PERSON><PERSON>("Animation Settings")]
    [Tooltip("Duration of the shake effect")]
    public float shakeDuration = 2f;
    
    [Toolt<PERSON>("Intensity of the shake movement")]
    public float shakeIntensity = 0.3f;
    
    [Tooltip("Scale multiplier for the scaling object")]
    public float scaleMultiplier = 1.2f;
    
    [<PERSON>lt<PERSON>("Duration for scale up/down animations")]
    public float scaleDuration = 0.2f;
    
    [Header("Audio (Optional)")]
    public AudioSource audioSource;
    public AudioClip completionSound;
    public AudioClip shakeSound;
    
    private bool isPlayingEffects = false;
    
    /// <summary>
    /// Triggers the complete sequence of completion effects
    /// </summary>
    public void TriggerCompletionEffects()
    {
        if (isPlayingEffects) return;
        
        StartCoroutine(PlayCompletionSequence());
    }
    
    /// <summary>
    /// Main coroutine that plays all completion effects in sequence
    /// </summary>
    IEnumerator PlayCompletionSequence()
    {
        isPlayingEffects = true;
        
        // Step 1: Play completion sound
        PlaySound(completionSound);
        
        // Step 2: Activate the first object
        if (objectToActivate != null)
        {
            objectToActivate.SetActive(true);
            Debug.Log($"Activated completion object: {objectToActivate.name}");
        }
        
        // Step 3: Scale and shake the specified object
        if (objectToScale != null)
        {
            yield return StartCoroutine(ScaleAndShakeSequence());
        }
        
        // Step 4: Final object state changes
        PerformFinalStateChanges();
        
        isPlayingEffects = false;
        Debug.Log("Mini-game completion effects finished!");
    }
    
    /// <summary>
    /// Handles the scale up, shake, and scale down sequence
    /// </summary>
    IEnumerator ScaleAndShakeSequence()
    {
        Vector3 originalScale = objectToScale.localScale;
        Vector3 originalPosition = objectToScale.localPosition;
        Vector3 targetScale = originalScale * scaleMultiplier;
        
        // Scale up animation
        yield return StartCoroutine(ScaleObject(originalScale, targetScale, scaleDuration));
        
        // Play shake sound
        PlaySound(shakeSound);
        
        // Shake animation
        yield return StartCoroutine(ShakeObject(originalPosition, shakeDuration, shakeIntensity));
        
        // Scale down animation
        yield return StartCoroutine(ScaleObject(targetScale, originalScale, scaleDuration));
        
        // Ensure object is back to original state
        objectToScale.localScale = originalScale;
        objectToScale.localPosition = originalPosition;
    }
    
    /// <summary>
    /// Smoothly scales an object from one scale to another
    /// </summary>
    IEnumerator ScaleObject(Vector3 fromScale, Vector3 toScale, float duration)
    {
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            float t = elapsed / duration;
            // Use smooth easing for better visual appeal
            t = Mathf.SmoothStep(0f, 1f, t);
            
            objectToScale.localScale = Vector3.Lerp(fromScale, toScale, t);
            
            elapsed += Time.deltaTime;
            yield return null;
        }
        
        objectToScale.localScale = toScale;
    }
    
    /// <summary>
    /// Shakes an object around its original position
    /// </summary>
    IEnumerator ShakeObject(Vector3 originalPosition, float duration, float intensity)
    {
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            // Generate random offset for shake
            Vector3 randomOffset = Random.insideUnitSphere * intensity;
            randomOffset.z = 0; // Keep it 2D for UI elements
            
            objectToScale.localPosition = originalPosition + randomOffset;
            
            elapsed += Time.deltaTime;
            yield return null;
        }
        
        // Return to original position
        objectToScale.localPosition = originalPosition;
    }
    
    /// <summary>
    /// Activates and deactivates final objects
    /// </summary>
    void PerformFinalStateChanges()
    {
        // Activate specified objects
        foreach (GameObject obj in objectsToActivateAfterShake)
        {
            if (obj != null)
            {
                obj.SetActive(true);
                Debug.Log($"Activated final object: {obj.name}");
            }
        }
        
        // Deactivate specified objects
        foreach (GameObject obj in objectsToDeactivateAfterShake)
        {
            if (obj != null)
            {
                obj.SetActive(false);
                Debug.Log($"Deactivated final object: {obj.name}");
            }
        }
    }
    
    /// <summary>
    /// Plays an audio clip if audio source and clip are available
    /// </summary>
    void PlaySound(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    /// <summary>
    /// Public method to check if effects are currently playing
    /// </summary>
    public bool IsPlayingEffects()
    {
        return isPlayingEffects;
    }
    
    /// <summary>
    /// Immediately stops all effects and resets states
    /// </summary>
    public void StopEffects()
    {
        StopAllCoroutines();
        isPlayingEffects = false;
        
        // Reset scale object if it exists
        if (objectToScale != null)
        {
            // Note: This assumes the original scale was (1,1,1)
            // For more robust reset, you'd want to store the original values
            objectToScale.localScale = Vector3.one;
        }
    }
    
    /// <summary>
    /// Validates the setup in the editor
    /// </summary>
    void OnValidate()
    {
        // Ensure positive values
        shakeDuration = Mathf.Max(0.1f, shakeDuration);
        shakeIntensity = Mathf.Max(0f, shakeIntensity);
        scaleMultiplier = Mathf.Max(0.1f, scaleMultiplier);
        scaleDuration = Mathf.Max(0.05f, scaleDuration);
    }
}
