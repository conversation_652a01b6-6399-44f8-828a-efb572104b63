Shader "UI/GlowingAura"
{
    Properties
    {
        _Color("Aura Color", Color) = (1,1,1,0.5)
        _EnablePulse("Enable Pulse (0 or 1)", Float) = 1.0
        _PulseSpeed("Pulse Speed", Float) = 2.0
        _AuraStrength("Aura Strength", Float) = 1.0
        _MinRadius("Min Radius", Float) = 0.2
        _MaxRadius("Max Radius", Float) = 0.5
        _RotationSpeed("Rotation Speed", Float) = 0.2
        _WobbleStrength("Wobble Strength", Float) = 0.05
        [HideInInspector]_MainTex("Sprite Texture", 2D) = "white" {}
    }

    SubShader
    {
        Tags { "Queue"="Transparent" "RenderType"="Transparent" "IgnoreProjector"="True" "PreviewType"="Plane" "CanUseSpriteAtlas"="True" }
        LOD 100

        Blend One One  // Additive glow
        Cull Off
        Lighting Off
        ZWrite Off
        ZTest Always

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            struct appdata_t
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            fixed4 _Color;
            float _EnablePulse;
            float _PulseSpeed;
            float _AuraStrength;
            float _MinRadius;
            float _MaxRadius;
            float _RotationSpeed;
            float _WobbleStrength;

            float2 Rotate(float2 pos, float angle)
            {
                float s = sin(angle);
                float c = cos(angle);
                return float2(
                    pos.x * c - pos.y * s,
                    pos.x * s + pos.y * c
                );
            }

            v2f vert(appdata_t v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                float2 uv = i.uv - 0.5;

                // Rotate UV to animate aura spin
                float angle = _Time.y * _RotationSpeed * 6.2831; // 2*PI
                float2 rotatedUV = Rotate(uv, angle);

                // Add wobble for irregularity (sin-based)
                rotatedUV.x += sin(_Time.y * 3.0 + rotatedUV.y * 20.0) * _WobbleStrength;
                rotatedUV.y += cos(_Time.y * 2.0 + rotatedUV.x * 15.0) * _WobbleStrength;

                // Distance from center
                float dist = length(rotatedUV);

                // Aura band (fade between min and max radius)
                // Create a ring: fade in from MaxRadius to MinRadius, then fade out from MinRadius to 0
                float outerFade = 1.0 - smoothstep(_MinRadius, _MaxRadius, dist);
                float innerFade = smoothstep(0.0, _MinRadius * 0.5, dist);
                float auraRange = outerFade * innerFade;

                // Pulsing
                float pulse = (sin(_Time.y * _PulseSpeed) + 1.0) * 0.5;
                pulse = lerp(1.0, pulse, saturate(_EnablePulse)); // 1.0 when disabled, pulsing when enabled
                float aura = auraRange * pulse * _AuraStrength;


                // For additive blending, we don't need to premultiply alpha
                fixed4 finalColor = _Color * aura;

                return finalColor;
            }
            ENDCG
        }
    }

    FallBack "UI/Default"
}
