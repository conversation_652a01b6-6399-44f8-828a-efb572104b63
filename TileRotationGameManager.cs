using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;

public class TileRotationGameManager : MonoBehaviour
{
    [Header("Game Setup")]
    public Button startButton;
    public RotatableTile[] tiles; // Assign all tile objects in the inspector
    
    [Header("Completion Effects")]
    [Tooltip("Optional: Use dedicated completion effects component")]
    public MiniGameCompletionEffects completionEffects;

    [Header("Manual Completion Effects (if not using MiniGameCompletionEffects)")]
    public GameObject objectToActivate; // Object that gets activated on completion
    public Transform objectToScale; // Object that gets scaled and shakes
    public GameObject[] objectsToActivateAfterShake = new GameObject[2]; // Objects activated after shake
    public GameObject[] objectsToDeactivateAfterShake = new GameObject[2]; // Objects deactivated after shake

    [Header("Shake Settings")]
    public float shakeDuration = 2f;
    public float shakeIntensity = 0.3f;
    public float scaleMultiplier = 1.2f; // How much to scale the object
    
    private bool gameStarted = false;
    private bool gameCompleted = false;
    private int correctTilesCount = 0;
    
    public bool GameStarted => gameStarted;
    public bool GameCompleted => gameCompleted;
    
    void Start()
    {
        // Setup start button
        if (startButton != null)
        {
            startButton.onClick.AddListener(StartGame);
        }
        
        // Initialize tiles
        InitializeTiles();
    }
    
    void InitializeTiles()
    {
        // Make sure all tiles are not interactable initially
        foreach (RotatableTile tile in tiles)
        {
            if (tile != null)
            {
                tile.SetInteractable(false);
                tile.SetGameManager(this);
            }
        }
    }
    
    public void StartGame()
    {
        if (gameStarted) return;
        
        gameStarted = true;
        gameCompleted = false;
        correctTilesCount = 0;
        
        // Hide start button
        if (startButton != null)
        {
            startButton.gameObject.SetActive(false);
        }
        
        // Randomize all tiles and make them interactable
        StartCoroutine(RandomizeTilesSequence());
    }
    
    IEnumerator RandomizeTilesSequence()
    {
        // Randomize each tile with a small delay for visual effect
        foreach (RotatableTile tile in tiles)
        {
            if (tile != null)
            {
                // Generate random rotation (90, 180, or 270 degrees)
                int randomRotations = Random.Range(1, 4); // 1, 2, or 3 rotations
                float targetRotation = randomRotations * 90f;
                
                // Start rotation animation
                StartCoroutine(tile.RotateToAngle(targetRotation, 0.5f));
                
                yield return new WaitForSeconds(0.1f); // Small delay between tiles
            }
        }
        
        // Wait a bit more for all rotations to complete
        yield return new WaitForSeconds(0.6f);
        
        // Make all tiles interactable
        foreach (RotatableTile tile in tiles)
        {
            if (tile != null)
            {
                tile.SetInteractable(true);
            }
        }
        
        Debug.Log("Game started! Click tiles to rotate them back to 0 degrees.");
    }
    
    public void OnTileRotated(RotatableTile tile)
    {
        if (!gameStarted || gameCompleted) return;
        
        // Check if this tile is now at correct angle (0 degrees)
        bool wasCorrect = tile.IsAtCorrectAngle();
        
        // Update correct tiles count
        UpdateCorrectTilesCount();
        
        // Check for game completion
        if (correctTilesCount >= tiles.Length)
        {
            OnGameCompleted();
        }
    }
    
    void UpdateCorrectTilesCount()
    {
        correctTilesCount = 0;
        foreach (RotatableTile tile in tiles)
        {
            if (tile != null && tile.IsAtCorrectAngle())
            {
                correctTilesCount++;
            }
        }
    }
    
    void OnGameCompleted()
    {
        if (gameCompleted) return;
        
        gameCompleted = true;
        
        // Make all tiles non-interactable
        foreach (RotatableTile tile in tiles)
        {
            if (tile != null)
            {
                tile.SetInteractable(false);
            }
        }
        
        Debug.Log("🎉 Tile Rotation Game Completed!");
        
        // Start completion effects sequence
        StartCoroutine(CompletionEffectsSequence());
    }
    
    IEnumerator CompletionEffectsSequence()
    {
        // Use dedicated completion effects component if available
        if (completionEffects != null)
        {
            completionEffects.TriggerCompletionEffects();
            yield break;
        }

        // Otherwise use manual completion effects
        // Step 1: Activate the first object
        if (objectToActivate != null)
        {
            objectToActivate.SetActive(true);
        }

        // Step 2: Scale and shake the specified object
        if (objectToScale != null)
        {
            yield return StartCoroutine(ScaleAndShakeObject());
        }

        // Step 3: Activate and deactivate final objects
        foreach (GameObject obj in objectsToActivateAfterShake)
        {
            if (obj != null)
            {
                obj.SetActive(true);
            }
        }

        foreach (GameObject obj in objectsToDeactivateAfterShake)
        {
            if (obj != null)
            {
                obj.SetActive(false);
            }
        }

        Debug.Log("Completion effects finished!");
    }
    
    IEnumerator ScaleAndShakeObject()
    {
        Vector3 originalScale = objectToScale.localScale;
        Vector3 originalPosition = objectToScale.localPosition;
        Vector3 targetScale = originalScale * scaleMultiplier;
        
        // Scale up quickly
        float scaleTime = 0.2f;
        float elapsed = 0f;
        
        while (elapsed < scaleTime)
        {
            objectToScale.localScale = Vector3.Lerp(originalScale, targetScale, elapsed / scaleTime);
            elapsed += Time.deltaTime;
            yield return null;
        }
        objectToScale.localScale = targetScale;
        
        // Shake for specified duration
        elapsed = 0f;
        while (elapsed < shakeDuration)
        {
            Vector3 randomOffset = Random.insideUnitSphere * shakeIntensity;
            randomOffset.z = 0; // Keep it 2D for UI
            objectToScale.localPosition = originalPosition + randomOffset;
            
            elapsed += Time.deltaTime;
            yield return null;
        }
        
        // Return to original position and scale
        objectToScale.localPosition = originalPosition;
        
        // Scale back down
        elapsed = 0f;
        while (elapsed < scaleTime)
        {
            objectToScale.localScale = Vector3.Lerp(targetScale, originalScale, elapsed / scaleTime);
            elapsed += Time.deltaTime;
            yield return null;
        }
        objectToScale.localScale = originalScale;
    }
    
    // Public method to reset the game
    public void ResetGame()
    {
        gameStarted = false;
        gameCompleted = false;
        correctTilesCount = 0;
        
        // Reset all tiles to 0 rotation
        foreach (RotatableTile tile in tiles)
        {
            if (tile != null)
            {
                tile.ResetTile();
            }
        }
        
        // Show start button again
        if (startButton != null)
        {
            startButton.gameObject.SetActive(true);
        }
        
        Debug.Log("Game reset!");
    }
}
