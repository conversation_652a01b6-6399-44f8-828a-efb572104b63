# Tile Rotation Mini-Game Setup Guide

This guide explains how to set up and use the Tile Rotation Mini-Game system.

## Overview

The Tile Rotation Mini-Game consists of:
- **TileRotationGameManager**: Main game controller
- **RotatableTile**: Component for individual tiles
- **MiniGameCompletionEffects**: Reusable completion effects system

## Setup Instructions

### 1. Scene Setup

1. Create a Canvas for your mini-game UI
2. Create a parent GameObject for your tile grid (e.g., "TileGrid")
3. Add your tile images as children of the grid
4. Create a Start button
5. Create objects for completion effects

### 2. Tile Setup

For each tile image:
1. Add a **Button** component
2. Add the **RotatableTile** script
3. Configure the RotatableTile settings:
   - `Rotation Speed`: How fast tiles rotate (default: 360°/sec)
   - `Correct Angle`: The target angle (usually 0°)
   - `Show Color Feedback`: Optional visual feedback
   - `Correct Color`: Color when tile is at correct angle
   - `Incorrect Color`: Color when tile is not at correct angle

### 3. Game Manager Setup

1. Create an empty GameObject and add **TileRotationGameManager**
2. Assign the following in the inspector:
   - `Start Button`: Your start button
   - `Tiles`: Array of all RotatableTile components
   - Completion effects objects (see below)

### 4. Completion Effects Setup

You have two options:

#### Option A: Use MiniGameCompletionEffects Component
1. Add **MiniGameCompletionEffects** to a GameObject
2. Configure the completion effects:
   - `Object To Activate`: Object activated immediately
   - `Object To Scale`: Object that scales and shakes
   - `Objects To Activate After Shake`: Objects activated after shake
   - `Objects To Deactivate After Shake`: Objects deactivated after shake
   - Animation settings (duration, intensity, etc.)
3. Assign this component to the Game Manager's `Completion Effects` field

#### Option B: Manual Setup
Configure the completion effects directly in TileRotationGameManager:
- `Object To Activate`: Object activated immediately
- `Object To Scale`: Object that scales and shakes
- `Objects To Activate After Shake`: Array of objects to activate
- `Objects To Deactivate After Shake`: Array of objects to deactivate
- Shake settings (duration, intensity, scale multiplier)

## Game Flow

1. **Initialization**: All tiles are set to non-interactable
2. **Start Game**: Player clicks start button
   - Start button disappears
   - Each tile rotates to a random angle (90°, 180°, or 270°)
   - Tiles become interactable
3. **Gameplay**: Player clicks tiles to rotate them by 90° each click
4. **Win Condition**: All tiles return to 0° rotation
5. **Completion Effects**: 
   - Object gets activated
   - Another object scales up, shakes for 2 seconds, then scales back
   - Final objects are activated/deactivated

## Customization Options

### RotatableTile Customization
- Change `rotationSpeed` for faster/slower animations
- Set different `correctAngle` for each tile if needed
- Enable `showColorFeedback` for visual hints
- Customize colors for correct/incorrect states

### Game Manager Customization
- Modify randomization logic in `RandomizeTilesSequence()`
- Adjust timing between tile randomizations
- Change win condition logic in `OnTileRotated()`

### Completion Effects Customization
- Adjust shake duration and intensity
- Change scale multiplier
- Add audio effects using the MiniGameCompletionEffects component
- Customize the sequence timing

## Testing

1. Assign all components and references
2. Play the scene
3. Click Start to begin the game
4. Click tiles to rotate them
5. Verify completion effects trigger when all tiles are at 0°

## Troubleshooting

### Common Issues:
1. **Tiles don't rotate**: Check that Button component is present and RotatableTile is properly configured
2. **Game doesn't start**: Verify Start button is assigned and has click listener
3. **Completion effects don't work**: Check that all effect objects are assigned
4. **Tiles rotate incorrectly**: Ensure tiles are using RectTransform (UI elements)

### Debug Features:
- Console logs show game state changes
- Use `ResetGame()` method to restart during testing
- Check `IsAtCorrectAngle()` for individual tile states

## Performance Notes

- The system uses coroutines for smooth animations
- Only one rotation animation per tile at a time
- Completion effects are optimized to avoid frame drops
- All rotations use normalized angles (0-360°) for consistency

## Extension Ideas

- Add difficulty levels (more tiles, faster rotation)
- Implement time limits
- Add score system based on number of clicks
- Create different tile patterns or shapes
- Add sound effects for tile rotations
- Implement hint system showing correct orientations
